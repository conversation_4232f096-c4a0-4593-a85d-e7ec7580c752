#!/usr/bin/env python3
"""
决策Agent

负责分析截图、生成思考过程和决定下一步动作（不包含坐标）
"""

import base64
import json
import re
import time
from typing import Tuple, Any

from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from loguru import logger
from openai import OpenAI
from pydantic import BaseModel, Field

from config.globalconfig import get_or_create_settings_ins
from src.domain.ui_task.mobile.aggregate.agent.verification_agent import expectation_verification_agent
from src.domain.ui_task.mobile.aggregate.prompt.decision_definition_prompt import *
from src.domain.ui_task.mobile.aggregate.prompt.decision_invoke_prompt import *
from src.domain.ui_task.mobile.android.screenshot_manager import \
    screenshot_manager, convert_screenshot_to_base64
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler, AgentExceptionHandler
from src.infra.model import get_chat_model


class DecisionResponse(BaseModel):
    """决策Agent响应的数据模型"""
    self_check: str = Field(default="", description="自检流程的自检结果")
    interface_analysis: str = Field(default="", description="当前界面分析")
    current_step_name: str = Field(default="", description="当前正在执行的步骤名称")
    action_decision: str = Field(default="", description="动作决策结果")
    instruction: str = Field(default="", description="操作指令")
    action: str = Field(default="", description="具体动作命令")


class DecisionAgent:
    """决策Agent - 负责分析和决策"""

    def __init__(self):
        self.model = get_chat_model()
        self.last_step_name = ""  # 记录上次的步骤名称，用于检测步骤变化

        # 创建输出解析器
        self.pydantic_parser = PydanticOutputParser(pydantic_object=DecisionResponse)

        # 尝试使用专门的修复模型，如果不存在则使用默认模型
        self.output_parser = OutputFixingParser.from_llm(
            parser=self.pydantic_parser,
            llm=get_chat_model(model_name="fix")
        )

    def analyze_and_decide(self, state: DeploymentState,
                           before_screenshot_path: str) -> tuple:
        """
            分析截图和测试用例上下文，生成思考过程和决定下一步动作

            Args:
                state: 当前状态
                before_screenshot_path: 截图的base64数据

            Returns:
                Tuple[parsed_fields, action_line]: 解析的JSON字段、动作命令
            """
        # 构建消息
        task_id = state["task_id"]
        try:
            image_data_base64 = convert_screenshot_to_base64(before_screenshot_path, task_id)
            messages = self._build_complete_test_case_messages(state, image_data_base64)
            start_time = time.time()
            prompt = ChatPromptTemplate.from_messages(messages=messages)
            chain = prompt | self.model
            output = chain.invoke({"timeout": 30})
            model_response = output.content

            # config = get_or_create_settings_ins()
            # client = OpenAI(
            #     base_url=config.paths.doubao_base_url,
            #     api_key=config.paths.doubao_api_key
            # )
            # chat_completion = client.chat.completions.create(
            #     model=config.paths.doubao_model,
            #     messages=messages,
            #     temperature=0.1,
            #     extra_body={
            #         "thinking": {
            #             "type": "disabled",  # 不使用深度思考能力
            #             #"type": "enabled",  # 使用深度思考能力
            #             # "type": "auto", # 模型自行判断是否使用深度思考能力
            #         }
            #     },
            # )
            # model_response = chat_completion.choices[0].message.content

            logger.info(f"[task_id: {task_id}] 决策模型完整响应: \n {model_response}")

            # 解析JSON响应
            parsed_fields, action_line = self._parse_json_response(model_response, state)

            # 处理步骤验证
            self.deal_step_verify(parsed_fields, state, task_id)

            # 立即记录决策日志
            self._log_decision_immediately(parsed_fields, action_line, task_id)

            # 决策结束时间
            end_time = time.time()
            logger.info(f"[task_id: {task_id}] 决策耗时: {end_time - start_time:.2f}s")
            return parsed_fields, action_line
        except Exception as e:
            return AgentExceptionHandler.handle_decision_agent_exception(task_id, e, state)

    def deal_step_verify(self, parsed_fields, state, task_id):
        # 记录当前步骤名称，用于步骤变化检测
        current_step_name = parsed_fields.get("current_step_name", "")
        step_name_changed = True if not current_step_name else not self._is_same_step_by_suffix(current_step_name,
                                                                                                self.last_step_name)
        parsed_fields["step_name_changed"] = step_name_changed
        parsed_fields["previous_step_name"] = self.last_step_name
        # 判断步骤切换方向（向上回退 vs 向下前进）
        step_direction = self._determine_step_direction(state, current_step_name, self.last_step_name)
        parsed_fields["step_direction"] = step_direction
        # 检查是否是验证失败后的回滚操作
        is_rollback_after_verification_failure = state.get("verification_failure_reason") is not None
        # 在决策后立即进行预期校验（仅当步骤向下切换时）
        if (step_name_changed and self.last_step_name and
                state.get("verification_mode") == "step_by_step" and
                not is_rollback_after_verification_failure and
                step_direction == "forward"):  # 只有向下切换时才验证
            verification_result = self._perform_expectation_verification(state)
            parsed_fields["expectation_verification_result"] = verification_result

            # 如果验证失败，不允许继续执行
            if not verification_result.get("verified", False):
                logger.warning(f"[task_id: {task_id}] ❌ Expectation verification failed, blocking execution")
                parsed_fields["execution_blocked"] = True
                parsed_fields["block_reason"] = verification_result.get("reason", "Expectation verification failed")
                # 验证失败时不更新last_step_name，保持原有状态以便下次重新验证
                logger.info(
                    f"[task_id: {task_id}] 🔄 Keeping last_step_name as '{self.last_step_name}' due to verification failure")
            else:
                # 验证成功时才更新last_step_name
                self.last_step_name = current_step_name
        elif is_rollback_after_verification_failure:
            logger.info(f"[task_id: {task_id}] ℹ️ Skipping verification due to rollback after verification failure")
            # 回滚后也更新last_step_name，因为这是重新执行的步骤
            self.last_step_name = current_step_name
        elif step_direction == "backward":
            logger.info(f"[task_id: {task_id}] ℹ️ Skipping verification due to backward step movement")
            # 向上回退时不验证，直接更新last_step_name
            self.last_step_name = current_step_name
        else:
            # 其他情况正常更新last_step_name
            self.last_step_name = current_step_name

    @staticmethod
    def _is_same_step_by_suffix(current_step: str, last_step: str) -> bool:
        """
        通过步骤名称结尾匹配判断是否为同一步骤
        去除特殊符号，仅匹配中文内容

        Args:
            current_step: 当前步骤名称
            last_step: 上一步骤名称

        Returns:
            bool: 是否为同一步骤
        """
        if not current_step or not last_step:
            return False

        # 如果完全相同，直接返回True
        if current_step == last_step:
            return True

        # 提取并清理中文内容
        current_cleaned = DecisionAgent._extract_chinese_content(current_step)
        last_cleaned = DecisionAgent._extract_chinese_content(last_step)

        if not current_cleaned or not last_cleaned:
            return False

        # 如果清理后完全相同，返回True
        if current_cleaned == last_cleaned:
            return True

        # 使用结尾匹配：检查其中一个是否以另一个结尾
        return current_cleaned.endswith(last_cleaned) or last_cleaned.endswith(current_cleaned)

    @staticmethod
    def _extract_chinese_content(text: str) -> str:
        """
        提取文本中的中文内容，去除特殊符号和转义字符

        Args:
            text: 原始文本

        Returns:
            str: 清理后的中文内容
        """
        if not text:
            return ""

        # 1. 处理转义字符
        # 将 \" 转换为 "，\' 转换为 '，\\ 转换为 \
        cleaned_text = text.replace('\\"', '"').replace("\\'", "'").replace('\\\\', '\\')

        # 2. 移除所有类型的引号，专注于核心内容匹配
        # 移除各种引号字符
        quote_chars = ['"', "'", '"', '"', "'", "'", "「", "」", "『", "』"]
        for quote in quote_chars:
            cleaned_text = cleaned_text.replace(quote, "")

        # 3. 提取中文字符、数字、常用标点符号
        # 保留中文字符、数字、空格以及常用标点符号（不包含引号）
        chinese_pattern = r'[\u4e00-\u9fff\u3400-\u4dbf\uf900-\ufaff0-9\s，。！？；：、（）【】《》〈〉〔〕〖〗〘〙〚〛]+'
        matches = re.findall(chinese_pattern, cleaned_text)

        # 4. 合并匹配的内容并去除多余空格
        result = ''.join(matches).strip()

        # 5. 将多个连续空格替换为单个空格
        result = re.sub(r'\s+', ' ', result)

        return result

    @staticmethod
    def _determine_step_direction(state: DeploymentState, current_step_name: str, last_step_name: str) -> str:
        """
        判断步骤切换方向

        Args:
            state: 当前状态
            current_step_name: 当前步骤名称
            last_step_name: 上一步骤名称

        Returns:
            "forward": 向下前进, "backward": 向上回退, "unknown": 无法判断
        """
        if not current_step_name or not last_step_name:
            return "unknown"

        task_steps = state.get("task_steps", [])
        if not task_steps:
            return "unknown"

        # 找到当前步骤和上一步骤在任务步骤列表中的索引
        current_index = -1
        last_index = -1

        for i, step_name in enumerate(task_steps):
            # 使用后缀匹配方式查找步骤
            if DecisionAgent._is_same_step_by_suffix(current_step_name, step_name):
                current_index = i
            if DecisionAgent._is_same_step_by_suffix(last_step_name, step_name):
                last_index = i

        if current_index == -1 or last_index == -1:
            return "unknown"

        if current_index > last_index:
            return "forward"  # 向下前进
        elif current_index < last_index:
            return "backward"  # 向上回退
        else:
            return "unknown"  # 相同步骤

    def _parse_json_response(self, model_response: str, state: DeploymentState) -> \
            Tuple[Dict[str, Any], str]:
        """
            解析模型的JSON响应，提取各个字段
            使用OutputFixingParser来纠正格式错误的JSON

            Args:
                model_response: 模型的完整响应
                state: 当前状态

            Returns:
                Tuple[parsed_fields, action_line]: 解析的字段字典、动作命令

            Raises:
                Exception: 当JSON解析和修复都失败时抛出异常
            """
        task_id = state['task_id']

        try:
            # 首先尝试直接解析JSON
            json_data = json.loads(model_response.strip())
            logger.info(f"[{task_id}] ✅ Direct JSON parsing successful")

        except json.JSONDecodeError as e:
            logger.warning(f"[{task_id}] ⚠️ Direct JSON parsing failed: {str(e)}")
            logger.info(f"[{task_id}] 🔧 Attempting to fix JSON format using OutputFixingParser...")

            try:
                # 使用OutputFixingParser来修复JSON格式
                parsed_response: DecisionResponse = self.output_parser.parse(model_response)
                json_data = parsed_response.model_dump()
                logger.info(f"[{task_id}] ✅ JSON format fixed successfully using OutputFixingParser")

            except Exception as fix_error:
                logger.error(f"[{task_id}] ❌ OutputFixingParser failed: {str(fix_error)}")
                logger.error(f"[{task_id}] Raw response: {model_response}")

                # 使用统一的异常处理方法
                TaskExceptionHandler.update_task_status_to_failed(
                    task_id,
                    f"JSON parsing and fixing failed in decision agent: {str(fix_error)}"
                )

                # 重新抛出异常，让外层异常处理机制处理
                raise fix_error

        # 构建返回的字段字典
        parsed_fields = {
            "self_check": json_data.get("self_check", ""),
            "interface_analysis": json_data.get("interface_analysis", ""),
            "current_step_name": json_data.get("current_step_name", ""),
            "action_decision": json_data.get("action_decision", ""),
            "instruction": json_data.get("instruction", ""),
            "action": json_data.get("action", "")
        }

        action_line = json_data.get("action", "")

        return parsed_fields, action_line

    def _build_complete_test_case_messages(self, state: DeploymentState,
                                           image_data_base64: str) -> list:

        execution_records, image_records, has_execution_history = self.get_history_parameters(state)

        decision_prompt = get_decision_definition_prompt(state, execution_records, has_execution_history)

        invoke_prompt = get_execution_invoke_prompt()

        messages = [
            {
                "role": "system",
                "content": decision_prompt
            },
            {
                "role": "system",
                "content": invoke_prompt
            }
        ]

        if has_execution_history:
            # 构建历史截图内容列表
            content_list = []

            # 添加历史截图（最近5轮）
            for i, record in enumerate(image_records):
                before_screenshot_path = record.get("before_screenshot")
                if before_screenshot_path and before_screenshot_path != "":
                    try:
                        full_path = screenshot_manager.get_screenshot_full_path(before_screenshot_path)
                        with open(full_path, "rb") as f:
                            before_image_content = f.read()
                        before_image_data_base64 = base64.b64encode(before_image_content).decode("utf-8")

                        execution_count = record.get("execution_count", i + 1)
                        content_list.extend([
                            {
                                "type": "text",
                                "text": f"第{execution_count}轮界面截图"
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{before_image_data_base64}",
                                    "detail": "high"
                                }
                            }
                        ])
                    except Exception as e:
                        logger.warning(f"Failed to load historical screenshot {before_screenshot_path}: {str(e)}")
                        continue

            # 如果有历史截图，添加包含所有图片的消息
            if content_list:
                messages.append(
                    {
                        "role": "user",
                        "content": "########## 执行记忆界面截图 ##########"
                    }
                )
                messages.append({
                    "role": "user",
                    "content": content_list
                })

                content_list.extend([
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}",
                            "detail": "high"
                        }
                    }
                ])
            else:
                # 如果没有历史截图，只添加当前截图
                messages.append({
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "########## 当前轮界面截图 ##########"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{image_data_base64}",
                                "detail": "high"
                            }
                        }
                    ]
                })
        else:
            messages.append({
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "########## 当前轮界面截图 ##########"
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{image_data_base64}"
                        }
                    }
                ]
            })

        messages.append({
            "role": "user",
            "content": get_user_invoke_prompt()
        })
        return messages

    @staticmethod
    def get_history_parameters(state) -> tuple[list[Any], list[Any], bool]:
        history = state.get("history", [])
        execution_records = [r for r in history if
                             r.get("action") == "enhanced_get_location" and
                             (r.get("decision_content") or r.get(
                                 "decision_fields") or r.get("model_response"))]
        image_records = execution_records[-2:] if len(execution_records) > 2 else execution_records
        has_execution_history = len(execution_records) > 0
        print(len(execution_records), len(image_records))
        return execution_records, image_records, has_execution_history

    @staticmethod
    def _perform_expectation_verification(
            state: DeploymentState) -> Dict[str, Any]:
        """
        执行预期结果验证

        Args:
            state: 当前状态

        Returns:
            验证结果
        """
        try:
            task_id = state["task_id"]

            # 获取任务步骤列表
            task_steps = state.get("task_steps", [])

            # 从历史记录中获取最近执行的步骤名称
            history = state.get("history", [])
            execution_records = [r for r in history if r.get("action") == "enhanced_get_location"]

            if not execution_records:
                logger.info(f"[task_id: {task_id}] ℹ️ No execution history found, no verification needed")
                return {"verified": True, "reason": "No execution history"}

            # 获取最近一次执行的决策字段
            latest_record = execution_records[-1]
            decision_fields = latest_record.get("decision_fields", {})
            executed_step_name = decision_fields.get("current_step_name", "")

            if not executed_step_name:
                logger.warning(f"[task_id: {task_id}] ⚠️ No step name found in latest execution record")
                return {"verified": True, "reason": "No step name in execution record"}

            # 根据步骤名称找到对应的索引（使用结尾匹配）
            previous_step_index = -1
            for i, step_name in enumerate(task_steps):
                if DecisionAgent._is_same_step_by_suffix(executed_step_name, step_name):
                    previous_step_index = i
                    break

            if previous_step_index == -1:
                logger.warning(f"[task_id: {task_id}] ⚠️ Step name '{executed_step_name}' not found in task_steps")
                return {"verified": True, "reason": f"Step name '{executed_step_name}' not found"}

            logger.info(
                f"[task_id: {task_id}] 🔍 Found executed step: '{executed_step_name}' at index {previous_step_index}")

            # 获取当前步骤的期望结果
            step_expected_results = state.get("step_expected_results", [])
            expected_text = None
            expected_image = None
            wait_time = 2.5  # 默认等待时间

            if previous_step_index < len(step_expected_results):
                expected_result = step_expected_results[previous_step_index]
                expected_text = expected_result.get("text")
                expected_image = expected_result.get("image")
                wait_time = expected_result.get("wait_time", 2.5)

            logger.info(
                f"[task_id: {task_id}] 🔍 Performing expectation verification for completed step {previous_step_index + 1}: '{executed_step_name}', wait_time: {wait_time}s")

            # 调用预期结果验证agent，验证当前步骤
            verification_result = expectation_verification_agent.verify_step_expectation(
                state=state,
                previous_step_index=previous_step_index,
                expected_text=expected_text,
                expected_image=expected_image,
                wait_time=wait_time
            )

            return verification_result

        except Exception as e:
            logger.error(f"[{state['task_id']}] ❌ Error in expectation verification: {str(e)}")

            # 使用统一的异常处理方法
            TaskExceptionHandler.update_task_status_to_failed(
                state['task_id'],
                f"Expectation verification error: {str(e)}"
            )

            return {"verified": False, "reason": f"Verification error: {str(e)}"}

    def _log_decision_immediately(self, parsed_fields: Dict[str, Any], action_line: str, task_id: str):
        """立即记录决策日志"""
        try:
            from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
            from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService

            step_name = parsed_fields.get("current_step_name", "")
            action_decision = parsed_fields.get("action_decision", "")

            decision_log = ExecutionLogService.create_decision_log(step_name, action_decision, action_line)
            task_persistence_service.append_execution_log_entries(task_id, [decision_log])
            logger.info(f"[{task_id}] 📝 Decision logged immediately")

        except Exception as e:
            logger.warning(f"[{task_id}] Failed to log decision: {str(e)}")
